import React, { useState, Ref, useEffect } from 'react';
import { But<PERSON>, Tabs, Table, Popconfirm, Modal } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { YTHPickUser } from 'yth-ui';
import { CurrentUser } from '@/Constant';
import { formatTree } from 'yth-ui/es/components/util/treeList';
import { Unit, User } from '@/service/system';
import baseApi from '@/service/baseApi';
import { IAction, IUserProps } from 'yth-ui/es/components/pickUser';
import { ColumnsType } from 'antd/lib/table';
import style from './index.module.less';

const { TabPane } = Tabs;

// 定义学习对象类型
interface DicDataType {
  code: string;
  text: string;
}

// 定义公司信息类型
interface CompanyDataType {
  id: string;
  parentId: string;
  children: unknown[];
  unitCode: string;
  unitName: string;
  unitType: string; // 用于判断是否为园区
  accountId: string | null;
  name: string;
  disabled: boolean;
  type: string;
  key: string;
  title: string;
}

// 扩展IUserProps接口，包含更多用户信息
interface ExtendedUserProps extends IUserProps {
  phone?: string;
  userCode?: string;
}

// 培训记录接口
interface TrainRecordItem {
  peopleName: string;
  learningObject: DicDataType[];
  ifFinishedStudy: string;
  learningDuration: string;
  scores: string;
  qualify: string;
  retake: string;
  employeeId: string;
  telephone: string;
  ifFinishedExam: string;
  peopleId: string;
}

const defaultQueryData: {
  learningObject: DicDataType[];
  companyId: CompanyDataType[];
  trainRecord: TrainRecordItem[];
} = {
  learningObject: [
    { code: 'A08A39A01', text: '主要负责人' },
    { code: 'A08A39A02', text: '安全管理人员' },
    { code: 'A08A39A03', text: '员工' },
    { code: 'A08A39A04', text: '承包商' },
  ],
  companyId: [
    {
      id: '***********',
      parentId: '700b9fb43efff78e2f04ade69844bf64',
      children: [],
      unitCode: '***********',
      unitName: '天驰物流有限责任公司',
      unitType: '0',
      accountId: null,
      name: '天驰物流有限责任公司',
      disabled: false,
      type: 'org',
      key: '***********',
      title: '天驰物流有限责任公司',
    },
  ],

  trainRecord: [
    {
      peopleName: '李强',
      learningObject: [
        {
          code: 'A08A39A01',
          text: '主要负责人',
        },
      ],
      ifFinishedStudy: '',
      learningDuration: '',
      scores: '',
      qualify: '',
      retake: '',
      employeeId: '100001',
      telephone: '',
      ifFinishedExam: '',
      peopleId: '1a270460ff4d1357ec842855825f4eee',
    },
    {
      peopleName: '安宁后台管理员',
      learningObject: [
        {
          code: 'A08A39A01',
          text: '主要负责人',
        },
      ],
      ifFinishedStudy: '',
      learningDuration: '',
      scores: '',
      qualify: '',
      retake: '',
      employeeId: '10010',
      telephone: '***********',
      ifFinishedExam: '',
      peopleId: 'af9f86206deff9f868b137b927e91542',
    },
    {
      peopleName: '寸清和',
      learningObject: [
        {
          code: 'A08A39A01',
          text: '主要负责人',
        },
      ],
      ifFinishedStudy: '',
      learningDuration: '',
      scores: '',
      qualify: '',
      retake: '',
      employeeId: '10032300',
      telephone: '13529125878',
      ifFinishedExam: '',
      peopleId: '03ad35e4bf59a9b08aac537ab8b0d695',
    },
    {
      peopleName: '李少康',
      learningObject: [
        {
          code: 'A08A39A02',
          text: '安全管理人员',
        },
      ],
      ifFinishedStudy: '',
      learningDuration: '',
      scores: '',
      qualify: '',
      retake: '',
      employeeId: '100002',
      telephone: '',
      ifFinishedExam: '',
      peopleId: '87d233d3b1bcf02e947f7d1efe23f1ad',
    },
    {
      peopleName: '唐志雄',
      learningObject: [
        {
          code: 'A08A39A02',
          text: '安全管理人员',
        },
      ],
      ifFinishedStudy: '',
      learningDuration: '',
      scores: '',
      qualify: '',
      retake: '',
      employeeId: '100003',
      telephone: '***********',
      ifFinishedExam: '',
      peopleId: 'c83bdf98ee9d53f7b9909ed24d06b236',
    },
    {
      peopleName: '苏伟',
      learningObject: [
        {
          code: 'A08A39A02',
          text: '安全管理人员',
        },
      ],
      ifFinishedStudy: '',
      learningDuration: '',
      scores: '',
      qualify: '',
      retake: '',
      employeeId: '100004',
      telephone: '***********',
      ifFinishedExam: '',
      peopleId: '370db0a4f2662cc09dd4c38d24c0ce0d',
    },
    {
      peopleName: '杨晓东',
      learningObject: [
        {
          code: 'A08A39A02',
          text: '安全管理人员',
        },
      ],
      ifFinishedStudy: '',
      learningDuration: '',
      scores: '',
      qualify: '',
      retake: '',
      employeeId: '100005',
      telephone: '',
      ifFinishedExam: '',
      peopleId: '2e8bb817d14b4a9bc1489529cea2a960',
    },
    {
      peopleName: '毛金龙',
      learningObject: [
        {
          code: 'A08A39A03',
          text: '员工',
        },
      ],
      ifFinishedStudy: '',
      learningDuration: '',
      scores: '',
      qualify: '',
      retake: '',
      employeeId: '100006',
      telephone: '',
      ifFinishedExam: '',
      peopleId: '7421d45aca20c3acd25378127d30be72',
    },
    {
      peopleName: '李波',
      learningObject: [
        {
          code: 'A08A39A03',
          text: '员工',
        },
      ],
      ifFinishedStudy: '',
      learningDuration: '',
      scores: '',
      qualify: '',
      retake: '',
      employeeId: '200001',
      telephone: '',
      ifFinishedExam: '',
      peopleId: 'dea6ad890f5aa8f106ebb4f39ca6c291',
    },
    {
      peopleName: '张超',
      learningObject: [
        {
          code: 'A08A39A03',
          text: '员工',
        },
      ],
      ifFinishedStudy: '',
      learningDuration: '',
      scores: '',
      qualify: '',
      retake: '',
      employeeId: '15987129374',
      telephone: '',
      ifFinishedExam: '',
      peopleId: 'f6d65dffb8da76e6e6840adc08ab2a66',
    },
    {
      peopleName: '吴学全',
      learningObject: [
        {
          code: 'A08A39A03',
          text: '员工',
        },
      ],
      ifFinishedStudy: '',
      learningDuration: '',
      scores: '',
      qualify: '',
      retake: '',
      employeeId: '200002',
      telephone: '13888219255',
      ifFinishedExam: '',
      peopleId: 'c35f0cd0397b78c5416fd6f75dcf041b',
    },
    {
      peopleName: '韦黎民',
      learningObject: [
        {
          code: 'A08A39A04',
          text: '承包商',
        },
      ],
      ifFinishedStudy: '',
      learningDuration: '',
      scores: '',
      qualify: '',
      retake: '',
      employeeId: '200003',
      telephone: '',
      ifFinishedExam: '',
      peopleId: '5b5b2c16ff43f103f927b175c2a0819e',
    },
    {
      peopleName: '李俊',
      learningObject: [
        {
          code: 'A08A39A04',
          text: '承包商',
        },
      ],
      ifFinishedStudy: '',
      learningDuration: '',
      scores: '',
      qualify: '',
      retake: '',
      employeeId: '300001',
      telephone: '13700623364',
      ifFinishedExam: '',
      peopleId: 'a8af3994b3f1814099a4f9b29318d3c4',
    },
  ],
};

type PropsTypes = {
  /** 完成回调 */
  onComplete?: (data: Record<string, string>[]) => void;
  /** 关闭弹框方法 */
  closeModal?: () => void;
  /** 默认查询参数 */
  defaultQuery?: Record<string, unknown> & {
    learningObject?: DicDataType[];
    companyId?: CompanyDataType[];
    trainRecord?: TrainRecordItem[];
  };
};

// 考核人员项接口
interface AssessmentPersonnelItem {
  key: string;
  name: string;
  objectType: string;
  employeeId: string;
  phone: string;
  userId: string;
}

/**
 * @description 考核人员安排页面
 * @returns React.FC
 */
const AssessmentPersonnelList: React.FC<PropsTypes> = ({
  onComplete, // 完成回调
  closeModal, // 关闭弹框方法
  defaultQuery = {},
}) => {
  /**
   * 获取公司信息数据，用于判断组织类型
   * 这些数据决定了人员管理的分类方式和界面展示
   * 如果有多条数据，只取第一条进行判断
   */
  const companyData: CompanyDataType[] = defaultQuery?.companyId || defaultQueryData.companyId;
  const firstCompany: CompanyDataType | undefined = companyData?.[0] || undefined;

  /**
   * 园区类型判断逻辑
   * 根据第一条公司数据中的unitType字段判断当前组织是否为园区
   * 园区和普通企业在人员管理上有不同的分类方式：
   * - 园区：统一管理，不区分具体职责分类
   * - 企业：按职责分类管理（主要负责人、安全管理人员、员工、承包商等）
   */
  const isPark: boolean = firstCompany?.unitType === '-1';

  /**
   * 动态标签页配置
   * 根据组织类型决定显示的标签页结构：
   * - 园区模式：只显示"园区人员"单一标签页
   * - 企业模式：显示多个职责分类标签页
   */
  const learningObjectData: DicDataType[] = isPark
    ? [{ code: 'PARK_PERSONNEL', text: '园区人员' }]
    : defaultQuery?.learningObject || defaultQueryData.learningObject;

  /**
   * 当前激活的标签页标识符
   * 默认选择第一个可用的标签页
   */
  const [activeTabKey, setActiveTabKey] = useState<string>(
    learningObjectData && learningObjectData.length > 0 ? learningObjectData[0].code : '',
  );

  /**
   * 用户选择器的当前选中数据
   * 存储用户在人员选择器中选中但尚未添加到表格的人员信息
   */
  const [userValue, setUserValue] = useState<IUserProps[]>([]);

  /**
   * 获取传入的培训记录数据，用于初始化人员信息
   * 如果有传入的培训记录，将其转换为组件内部的数据格式
   */
  const trainRecordData: TrainRecordItem[] =
    defaultQuery?.trainRecord || defaultQueryData.trainRecord;

  /**
   * 将培训记录数据转换为组件内部的人员数据格式
   * 根据每个人员的learningObject分类，将其分配到对应的标签页中
   */
  const convertTrainRecordToPersonnelData: () => Record<string, AssessmentPersonnelItem[]> = () => {
    const result: Record<string, AssessmentPersonnelItem[]> = {};

    trainRecordData.forEach((record) => {
      // 为每个人员的每个学习对象创建人员项
      record.learningObject.forEach((learningObj) => {
        const tabKey: string = learningObj.code;

        if (!result[tabKey]) {
          result[tabKey] = [];
        }

        // 检查是否已存在相同的人员（避免重复）
        const existingPerson: AssessmentPersonnelItem | undefined = result[tabKey].find(
          (person) => person.employeeId === record.employeeId,
        );

        if (!existingPerson) {
          result[tabKey].push({
            key: record.peopleId || `${Date.now()}-${Math.random()}`,
            name: record.peopleName,
            objectType: learningObj.text,
            employeeId: record.employeeId,
            phone: record.telephone,
            userId: record.peopleId,
          });
        }
      });
    });

    return result;
  };

  /**
   * 所有标签页的人员数据存储
   * 结构：{ [tabKey]: AssessmentPersonnelItem[] }
   * 每个标签页维护独立的人员列表，支持跨标签页的数据管理和唯一性检查
   * 初始化时使用传入的培训记录数据
   */
  const [personnelData, setPersonnelData] = useState<Record<string, AssessmentPersonnelItem[]>>(
    convertTrainRecordToPersonnelData(),
  );

  // 用户选择组件的ref
  const PickRef: Ref<IAction> = React.useRef();

  // 确保activeTabKey有值（防御性编程）
  useEffect(() => {
    if (learningObjectData && learningObjectData.length > 0 && !activeTabKey) {
      setActiveTabKey(learningObjectData[0].code);
    }
  }, [learningObjectData, activeTabKey]);

  /**
   * 标签页切换处理函数
   *
   * 功能：
   * 1. 更新当前激活的标签页
   * 2. 清空用户选择器状态
   *
   * 设计考虑：
   * - 避免用户在不同标签页间切换时产生混淆
   * - 确保每次选择人员都是针对当前标签页的明确操作
   * - 提供清晰的操作边界和用户反馈
   */
  const handleTabChange: (key: string) => void = (key: string) => {
    setActiveTabKey(key);
    setUserValue([]); // 清空选择器，要求用户重新选择
  };

  /**
   * 删除人员处理函数
   * 从指定标签页中删除选定的人员，并同步更新相关状态
   *
   * @param tabKey - 标签页标识符
   * @param personKey - 人员记录的唯一标识符
   */
  const handleDeletePerson: (tabKey: string, personKey: string) => void = (
    tabKey: string,
    personKey: string,
  ) => {
    const personToDelete: AssessmentPersonnelItem | undefined = personnelData[tabKey]?.find(
      (item) => item.key === personKey,
    );

    if (!personToDelete) {
      return;
    }

    // 从指定标签页的人员数据中移除该人员
    setPersonnelData((prev) => ({
      ...prev,
      [tabKey]: (prev[tabKey] || []).filter((item) => item.key !== personKey),
    }));

    /**
     * 清空用户选择器状态
     * 原因：删除操作后清空选择器可以避免界面状态不一致
     * 用户体验：提供清晰的操作反馈，用户知道需要重新选择
     */
    setUserValue([]);
  };

  // 获取Table的列配置
  const getTableColumns: (tabKey: string) => ColumnsType<AssessmentPersonnelItem> = (tabKey) => [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: '类型',
      dataIndex: 'objectType',
      key: 'objectType',
      width: 120,
    },
    {
      title: '工号',
      dataIndex: 'employeeId',
      key: 'employeeId',
      width: 120,
    },
    {
      title: '联系方式',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: string, record: AssessmentPersonnelItem) => (
        <Popconfirm
          title="确定要删除这个人员吗？"
          onConfirm={() => handleDeletePerson(tabKey, record.key)}
          icon={null}
          okText="确定"
          style={{ fontSize: '12px' }}
          cancelText="取消"
        >
          <Button type="link" danger icon={<DeleteOutlined />} size="small">
            删除
          </Button>
        </Popconfirm>
      ),
    },
  ];

  // 渲染tab内容
  const renderTabContent: (tabItem: DicDataType) => React.ReactNode = (tabItem) => {
    const currentData: AssessmentPersonnelItem[] = personnelData[tabItem.code] || [];

    return (
      <div>
        <div className={style['add-btn-container']}>
          <Button
            type="primary"
            onClick={() => {
              PickRef?.current?.click();
            }}
          >
            添加
          </Button>
        </div>
        <Table<AssessmentPersonnelItem>
          columns={getTableColumns(tabItem.code)}
          dataSource={currentData}
          size="small"
          bordered
          locale={{ emptyText: '暂无数据' }}
          rowClassName={(_, index) => (index % 2 === 1 ? 'table-row-striped' : '')}
        />
      </div>
    );
  };

  return (
    <div className={style['assessment-personnel-container']}>
      <div>
        <Tabs activeKey={activeTabKey} onChange={handleTabChange}>
          {learningObjectData.map((item: DicDataType) => (
            <TabPane
              tab={
                <span>
                  {item.text}
                  {/* 显示当前tab的数据数量 */}
                  {personnelData[item.code] && personnelData[item.code].length > 0 && (
                    <span style={{ marginLeft: 4, color: '#1890ff' }}>
                      ({personnelData[item.code].length})
                    </span>
                  )}
                </span>
              }
              key={item.code}
            />
          ))}
        </Tabs>

        {/* 渲染当前激活tab的内容 */}
        {activeTabKey && (
          <div>
            {learningObjectData
              .filter((item) => item.code === activeTabKey)
              .map((item) => renderTabContent(item))}
          </div>
        )}
      </div>
      <div className={style['pick-user-container']}>
        <YTHPickUser
          defaultOrganize={{
            id: CurrentUser()?.accountId,
            name: '',
            type: 'org',
          }}
          value={userValue}
          actionRef={PickRef}
          requestOrganize={async () => {
            const resData: Unit = await baseApi.getUnitTree();
            return formatTree(resData, 'unitType', 'unitName');
          }}
          searchMode="1"
          multiple
          onChange={(users: ExtendedUserProps[]) => {
            setUserValue(users);
          }}
          onOk={(users: ExtendedUserProps[]) => {
            setUserValue([]);
            // 根据当前激活的tab获取对应的tab信息
            const currentTabData: DicDataType | undefined = learningObjectData.find(
              (item) => item.code === activeTabKey,
            );
            if (!currentTabData) {
              return;
            }

            // 获取当前tab已有的人员数据
            const existingPersonnel: AssessmentPersonnelItem[] =
              personnelData[currentTabData.code] || [];

            // 将选中的用户数据转换为AssessmentPersonnelItem格式
            const formattedUsers: AssessmentPersonnelItem[] = users.map(
              (user: ExtendedUserProps) => ({
                key: user.id || `${Date.now()}-${Math.random()}`, // 生成唯一key
                name: user.name, // 用户姓名
                objectType: currentTabData.text, // 对象类型，根据当前tab设置
                employeeId: user.userCode || user.id, // 优先使用工号，否则使用用户ID
                phone: user.phone || '', // 使用用户的联系电话，如果没有则为空
                userId: user.id,
              }),
            );

            /**
             * 人员重复性检查逻辑
             *
             * 业务规则：
             * 1. 同一标签页内重复：静默过滤，用户无感知（正常行为）
             * 2. 跨标签页重复：阻止添加并提示用户（业务规则违反）
             *
             * 实现策略：
             * - 分离当前标签页和其他标签页的数据
             * - 分别进行重复检查，采用不同的处理方式
             */

            // 收集其他标签页的所有人员数据，用于跨标签页重复检查
            const otherTabsPersonnel: AssessmentPersonnelItem[] = [];
            Object.keys(personnelData).forEach((tabKey) => {
              if (tabKey !== currentTabData.code) {
                otherTabsPersonnel.push(...(personnelData[tabKey] || []));
              }
            });

            // 存储跨标签页重复的用户信息，用于生成提示消息
            const crossTabDuplicateUsers: string[] = [];

            /**
             * 过滤和检查新添加的用户
             * 返回可以实际添加的用户列表
             */
            const newUsers: AssessmentPersonnelItem[] = formattedUsers.filter((newUser) => {
              /**
               * 第一层检查：当前标签页内重复
               * 处理方式：静默过滤，不影响用户体验
               * 场景：用户重复选择同一批人员时的容错处理
               */
              const existsInCurrentTab: boolean = existingPersonnel.some(
                (existing) => existing.employeeId === newUser.employeeId,
              );
              if (existsInCurrentTab) {
                return false; // 静默过滤
              }

              /**
               * 第二层检查：跨标签页重复
               * 处理方式：阻止添加并记录提示信息
               * 场景：违反"一人一岗"业务规则的情况
               */
              const existingUserInOtherTab: AssessmentPersonnelItem | undefined =
                otherTabsPersonnel.find((existing) => existing.employeeId === newUser.employeeId);
              if (existingUserInOtherTab) {
                crossTabDuplicateUsers.push(
                  `${newUser.name}(${newUser.employeeId}) 已存在于 ${existingUserInOtherTab.objectType} 中`,
                );
                return false; // 阻止添加
              }

              return true; // 通过所有检查，可以添加
            });

            // 显示跨标签页重复的警告信息，使用Modal确保换行显示正确
            if (crossTabDuplicateUsers.length > 0) {
              const content: React.ReactNode[] = crossTabDuplicateUsers.map((userInfo) => (
                <div key={userInfo} style={{ marginBottom: 4 }}>
                  {userInfo}
                </div>
              ));

              // 使用Modal.warning来显示多行信息，确保格式正确
              Modal.warning({
                title: '以下人员已存在，无法重复添加',
                content: <div>{content}</div>,
                okText: '确定',
              });
            }

            // 只添加不重复的用户数据到对应tab的状态中
            setPersonnelData((prev) => ({
              ...prev,
              [currentTabData.code]: [...existingPersonnel, ...newUsers],
            }));

            // 清空选择器的值
            setUserValue([]);
          }}
          remoteSearch
          requestUser={async (organize) => {
            const resData: User[] = await baseApi.getUserList(organize.id);
            const newData: ExtendedUserProps[] = [];
            resData.forEach((item: User) => {
              newData.push({
                id: item.id,
                name: item.realName,
                type: 'user',
                // 扩展用户信息，便于后续表单数据映射
                phone: item.phone,
                userCode: item.userCode,
              });
            });
            return newData;
          }}
        />
      </div>

      <div className={style['btn-container']}>
        <Button
          onClick={() => {
            // 清空所有数据
            setPersonnelData({});
            setUserValue([]);
            closeModal?.();
          }}
        >
          关闭
        </Button>
        <Button
          type="primary"
          style={{ marginLeft: '10px' }}
          onClick={() => {
            // 将人员数据转换为原来的格式
            const formattedData: Record<string, string>[] = [];
            Object.keys(personnelData).forEach((tabKey) => {
              const tabData: AssessmentPersonnelItem[] = personnelData[tabKey];
              tabData.forEach((person) => {
                formattedData.push({
                  name: person.name,
                  objectType: person.objectType,
                  employeeId: person.employeeId,
                  phone: person.phone,
                  tabKey, // 添加tab标识
                  userId: person.userId,
                });
              });
            });
            onComplete?.(formattedData);
            closeModal?.();
          }}
        >
          保存
        </Button>
      </div>
    </div>
  );
};

export default AssessmentPersonnelList;
